# Java CPU Monitor Agent

[![CI/CD Pipeline](https://github.com/YOUR_USERNAME/java-cpu-monitor-agent/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/YOUR_USERNAME/java-cpu-monitor-agent/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Java Version](https://img.shields.io/badge/Java-8%2B-blue.svg)](https://www.oracle.com/java/)
[![Docker](https://img.shields.io/badge/Docker-Supported-blue.svg)](https://www.docker.com/)
[![Release](https://img.shields.io/github/v/release/YOUR_USERNAME/java-cpu-monitor-agent)](https://github.com/YOUR_USERNAME/java-cpu-monitor-agent/releases)

一个基于Java Agent技术的CPU性能监控和分析工具，能够实时监控Java应用程序的CPU使用情况，识别热点方法，并提供详细的性能分析报告。

## 🌟 特性亮点

- **🔥 零侵入监控** - 基于Java Agent，无需修改应用代码
- **⚡ 实时分析** - 毫秒级CPU使用率监控和热点识别
- **🐳 Docker友好** - 完美支持容器化部署
- **📊 多格式报告** - JSON和控制台格式，便于集成
- **🎯 智能建议** - 基于监控数据的性能优化建议
- **🔧 高度可配置** - 灵活的采样间隔和阈值设置

## 功能特性

### 🔥 核心功能
- **实时CPU监控** - 持续监控应用程序和线程级别的CPU使用率
- **热点方法识别** - 通过字节码增强技术识别CPU密集型方法
- **调用堆栈追踪** - 提供详细的方法调用性能数据
- **轻量级报告** - 生成JSON和控制台格式的性能分析报告
- **智能建议** - 基于分析结果提供性能优化建议

### 🛠 技术特性
- **零侵入** - 基于Java Agent，无需修改应用代码
- **高性能** - 使用ByteBuddy进行高效的字节码操作
- **低开销** - 智能采样和过滤，最小化对应用性能的影响
- **实时监控** - 支持动态attach到运行中的Java进程
- **多线程安全** - 完全支持多线程环境下的性能监控

## 快速开始

### 环境要求
- **Java 11 或更高版本** (推荐)
- Java 8 或更高版本 (最低要求)
- Maven 3.6 或更高版本

### Java版本兼容性
- ✅ **Java 11** - 推荐版本，支持所有新特性
- ✅ **Java 17** - 完全支持，LTS版本
- ✅ **Java 21** - 完全支持，最新LTS版本
- ✅ **Java 8** - 最低支持版本

### 构建项目

#### Java 11+ 推荐构建方式
```bash
# 克隆项目
git clone https://github.com/YOUR_USERNAME/java-cpu-monitor-agent.git
cd java-cpu-monitor-agent

# Java 11优化构建
./build-java11.sh

# 生成的Agent JAR文件位于
# target/java-cpu-monitor-agent-1.0.0.jar
```

#### 通用构建方式 (Java 8+)
```bash
# 编译和打包
mvn clean package

# 或使用通用构建脚本
./build.sh
```

### 使用方法

#### 1. 启动时加载Agent（推荐）

**Java 11+ 优化版本：**
```bash
java --add-opens java.management/sun.management=ALL-UNNAMED \
     -XX:+UseG1GC -XX:+UseStringDeduplication \
     -javaagent:target/java-cpu-monitor-agent-1.0.0.jar=interval=5000,threshold=70,topMethods=10 \
     -cp target/test-classes \
     com.monitor.agent.test.TestApplication
```

**Java 8+ 通用版本：**
```bash
java -javaagent:target/java-cpu-monitor-agent-1.0.0.jar=interval=5000,threshold=70,topMethods=10 \
     -cp target/test-classes \
     com.monitor.agent.test.TestApplication
```

#### 2. 动态附加到运行中的进程

```bash
# 首先启动目标应用
java -cp target/test-classes com.monitor.agent.test.TestApplication &

# 获取进程ID
jps

# 动态附加Agent
java -jar target/java-cpu-monitor-agent-1.0.0.jar <PID>
```

### 配置参数

Agent支持以下配置参数：

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `interval` | 5000 | CPU采样间隔（毫秒） |
| `threshold` | 70 | CPU使用率阈值（百分比） |
| `topMethods` | 10 | 显示的热点方法数量 |

**示例配置：**
```bash
-javaagent:java-cpu-monitor-agent.jar=interval=3000,threshold=80,topMethods=15
```

## 输出示例

### 控制台输出
```
================================================================================
CPU Performance Analysis Report - 2024-01-15 14:30:25
================================================================================
CPU Usage: Current=75.23%, Max=89.45%, Avg=68.12% (Samples: 120)

Top 10 CPU-Intensive Methods:
--------------------------------------------------------------------------------
com.example.service.DataProcessor.processLargeDataSet()     1250 calls    2847.32ms total   2.28ms avg
com.example.util.StringUtils.performComplexRegex()          890 calls    1923.45ms total   2.16ms avg
com.example.algorithm.SortingAlgorithm.quickSort()          456 calls    1456.78ms total   3.19ms avg
com.example.math.Calculator.performComplexCalculation()     2340 calls    1234.56ms total   0.53ms avg
================================================================================
```

### JSON报告格式
```json
{
  "timestamp": "2024-01-15 14:30:25",
  "reportType": "CPU Performance Analysis",
  "version": "1.0.0",
  "cpuMetrics": {
    "currentUsage": "75.23%",
    "maxUsage": "89.45%",
    "avgUsage": "68.12%",
    "sampleCount": 120,
    "recentTrend": ["70.12%", "72.34%", "75.23%"]
  },
  "topMethods": [
    {
      "methodSignature": "com.example.service.DataProcessor.processLargeDataSet()",
      "callCount": 1250,
      "totalDurationMs": "2847.32",
      "avgDurationMs": "2.28",
      "cpuUsagePercent": "15.67%"
    }
  ],
  "summary": {
    "totalMethodCalls": 45678,
    "uniqueMethodsCount": 234,
    "recommendations": [
      "🔥 热点方法: com.example.service.DataProcessor.processLargeDataSet() (总耗时: 2847.32ms)",
      "⚠️ 当前CPU使用率过高 (75.23%)，建议检查热点方法"
    ]
  }
}
```

## Java 11+ 特性优化

### 🚀 性能优化
- **String.repeat()** - 使用Java 11原生字符串重复方法，性能更优
- **G1垃圾收集器** - 推荐使用G1GC，减少监控开销
- **字符串去重** - 启用字符串去重功能，降低内存使用

### 🔧 JVM参数优化
```bash
# Java 11+ 推荐JVM参数
java --add-opens java.management/sun.management=ALL-UNNAMED \
     -XX:+UseG1GC \
     -XX:+UseStringDeduplication \
     -XX:MaxGCPauseMillis=200 \
     -javaagent:java-cpu-monitor-agent.jar \
     YourApplication
```

### 📊 模块系统兼容
- 完全兼容Java 9+ 模块系统
- 使用`--add-opens`参数访问JDK内部API
- 支持模块化应用监控

## 高级用法

### 自定义过滤规则

Agent默认会忽略JDK内部类和常见的getter/setter方法。如需自定义过滤规则，可以修改`ClassTransformer`类：

```java
// 在ClassTransformer.java中添加自定义过滤规则
.ignore(ElementMatchers.nameStartsWith("com.yourcompany.ignored."))
```

### 性能调优建议

1. **采样间隔调整**
   - 高频监控：`interval=1000`（1秒）
   - 常规监控：`interval=5000`（5秒，推荐）
   - 低频监控：`interval=10000`（10秒）

2. **阈值设置**
   - 开发环境：`threshold=50`
   - 测试环境：`threshold=70`
   - 生产环境：`threshold=80`

3. **方法过滤**
   - 避免监控简单的getter/setter方法
   - 专注于业务逻辑复杂的方法
   - 可以通过修改字节码转换规则来精确控制

## 故障排除

### 常见问题

**Q: Agent无法启动，提示"Failed to start agent"**
A: 检查Java版本是否为8+，确保Agent JAR文件路径正确。

**Q: 看不到任何监控输出**
A: 确认目标应用有足够的CPU活动，可以降低threshold参数值。

**Q: 性能影响过大**
A: 增加interval参数值，减少topMethods数量，或添加更多过滤规则。

**Q: 在某些JVM上出现权限错误**
A: 添加JVM参数：`-Djdk.attach.allowAttachSelf=true`

### 调试模式

启用详细日志输出：
```bash
java -Dcom.monitor.agent.debug=true -javaagent:java-cpu-monitor-agent.jar YourApp
```

## 架构设计

### 核心组件

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CpuProfiler   │    │  ClassTransformer │    │  DataCollector  │
│                 │    │                  │    │                 │
│ - CPU采样       │    │ - 字节码增强      │    │ - 数据收集      │
│ - 线程监控      │    │ - 方法拦截        │    │ - 指标计算      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ ReportGenerator │
                    │                 │
                    │ - 报告生成      │
                    │ - 格式化输出    │
                    └─────────────────┘
```

### 技术栈
- **Java Agent API** - 字节码插桩入口
- **ByteBuddy** - 高性能字节码操作库
- **ThreadMXBean** - JVM内置线程监控
- **Jackson** - JSON序列化和反序列化
- **Maven** - 项目构建和依赖管理

## 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境设置
```bash
git clone <repository-url>
cd java-monitor-agent
mvn clean compile
```

### 运行测试
```bash
mvn test
```

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

我们欢迎各种形式的贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

### 快速开始贡献
1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [GitHub仓库](https://github.com/YOUR_USERNAME/java-cpu-monitor-agent)
- [问题反馈](https://github.com/YOUR_USERNAME/java-cpu-monitor-agent/issues)
- [发布页面](https://github.com/YOUR_USERNAME/java-cpu-monitor-agent/releases)
- [讨论区](https://github.com/YOUR_USERNAME/java-cpu-monitor-agent/discussions)

## ⭐ Star History

如果这个项目对您有帮助，请给我们一个 ⭐！

[![Star History Chart](https://api.star-history.com/svg?repos=YOUR_USERNAME/java-cpu-monitor-agent&type=Date)](https://star-history.com/#YOUR_USERNAME/java-cpu-monitor-agent&Date)

## 更新日志

查看 [CHANGELOG.md](CHANGELOG.md) 了解详细的版本更新信息。

### v1.0.0 (2024-06-12)
- 🎉 首次发布
- ✅ 实现基础CPU监控功能
- ✅ 支持热点方法识别
- ✅ 提供JSON和控制台报告格式
- ✅ 支持动态配置参数
- ✅ 完整的Docker部署支持
